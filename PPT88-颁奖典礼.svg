<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 舞台灯光效果背景 -->
  <defs>
    <radialGradient id="spotlight1" cx="25%" cy="20%" r="30%">
      <stop offset="0%" style="stop-color:#FFB366;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#FFB366;stop-opacity:0"/>
    </radialGradient>
    <radialGradient id="spotlight2" cx="75%" cy="20%" r="30%">
      <stop offset="0%" style="stop-color:#4472C4;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#4472C4;stop-opacity:0"/>
    </radialGradient>
  </defs>
  <rect width="1920" height="1080" fill="url(#spotlight1)"/>
  <rect width="1920" height="1080" fill="url(#spotlight2)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,150 Q960,100 1920,150" stroke="#4472C4" stroke-width="2" fill="none" opacity="0.2"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#2F4F4F">为英雄加冕</text>
  
  <!-- 四个奖杯 -->
  <!-- 最佳团队 -->
  <g transform="translate(240,350)">
    <g transform="translate(200,80)">
      <!-- 奖杯 -->
      <ellipse cx="0" cy="40" rx="40" ry="50" fill="#FFB366"/>
      <rect x="-30" y="90" width="60" height="20" rx="10" fill="#4472C4"/>
      <rect x="-15" y="110" width="30" height="40" rx="5" fill="#2F4F4F"/>
      <!-- 手柄 -->
      <ellipse cx="-50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <ellipse cx="50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <!-- 装饰 -->
      <circle cx="0" cy="20" r="15" fill="#4472C4"/>
      <text x="0" y="28" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#ffffff">1</text>
    </g>
    <text x="200" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#4472C4">最佳团队</text>
    <text x="200" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#2F4F4F">(Best Team)</text>
  </g>
  
  <!-- 进步之星 -->
  <g transform="translate(720,350)">
    <g transform="translate(200,80)">
      <!-- 奖杯 -->
      <ellipse cx="0" cy="40" rx="40" ry="50" fill="#FFB366"/>
      <rect x="-30" y="90" width="60" height="20" rx="10" fill="#4472C4"/>
      <rect x="-15" y="110" width="30" height="40" rx="5" fill="#2F4F4F"/>
      <!-- 手柄 -->
      <ellipse cx="-50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <ellipse cx="50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <!-- 星星装饰 -->
      <polygon points="0,5 3,15 13,15 6,22 8,32 0,26 -8,32 -6,22 -13,15 -3,15" fill="#4472C4"/>
    </g>
    <text x="200" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#4472C4">进步之星</text>
    <text x="200" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#2F4F4F">(Most Improved Star)</text>
  </g>
  
  <!-- 随销突破王 -->
  <g transform="translate(240,600)">
    <g transform="translate(200,80)">
      <!-- 奖杯 -->
      <ellipse cx="0" cy="40" rx="40" ry="50" fill="#FFB366"/>
      <rect x="-30" y="90" width="60" height="20" rx="10" fill="#4472C4"/>
      <rect x="-15" y="110" width="30" height="40" rx="5" fill="#2F4F4F"/>
      <!-- 手柄 -->
      <ellipse cx="-50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <ellipse cx="50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <!-- 皇冠装饰 -->
      <path d="M-20,5 L-10,15 L0,5 L10,15 L20,5 L15,25 L-15,25 Z" fill="#4472C4"/>
    </g>
    <text x="200" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#4472C4">随销突破王</text>
    <text x="200" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#2F4F4F">(Upselling Breakthrough King)</text>
  </g>
  
  <!-- 金牌智家顾问 -->
  <g transform="translate(720,600)">
    <g transform="translate(200,80)">
      <!-- 奖杯 -->
      <ellipse cx="0" cy="40" rx="40" ry="50" fill="#FFB366"/>
      <rect x="-30" y="90" width="60" height="20" rx="10" fill="#4472C4"/>
      <rect x="-15" y="110" width="30" height="40" rx="5" fill="#2F4F4F"/>
      <!-- 手柄 -->
      <ellipse cx="-50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <ellipse cx="50" cy="20" rx="8" ry="15" fill="none" stroke="#FFB366" stroke-width="4"/>
      <!-- 金牌装饰 -->
      <circle cx="0" cy="20" r="18" fill="#FFB366"/>
      <circle cx="0" cy="20" r="12" fill="#4472C4"/>
      <text x="0" y="26" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#ffffff">金</text>
    </g>
    <text x="200" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#4472C4">金牌智家顾问</text>
    <text x="200" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#2F4F4F">(Gold-Standard Smart Home Advisor)</text>
  </g>
  
  <!-- 右侧装饰区域 -->
  <g transform="translate(1400,500)">
    <rect x="-150" y="-100" width="300" height="200" rx="20" fill="#FFF8E1" stroke="#FFB366" stroke-width="3"/>
    <!-- 庆祝元素 -->
    <g transform="translate(0,0)">
      <!-- 彩带 -->
      <path d="M-80,-60 Q-60,-40 -40,-60 Q-20,-40 0,-60 Q20,-40 40,-60 Q60,-40 80,-60" stroke="#FF6B6B" stroke-width="4" fill="none"/>
      <path d="M-80,60 Q-60,40 -40,60 Q-20,40 0,60 Q20,40 40,60 Q60,40 80,60" stroke="#4472C4" stroke-width="4" fill="none"/>
      <!-- 烟花 -->
      <g transform="translate(-40,-20)">
        <circle cx="0" cy="0" r="3" fill="#FFB366"/>
        <path d="M0,-15 L0,15 M-15,0 L15,0 M-10,-10 L10,10 M-10,10 L10,-10" stroke="#FFB366" stroke-width="2"/>
      </g>
      <g transform="translate(40,20)">
        <circle cx="0" cy="0" r="2" fill="#4472C4"/>
        <path d="M0,-10 L0,10 M-10,0 L10,0 M-7,-7 L7,7 M-7,7 L7,-7" stroke="#4472C4" stroke-width="2"/>
      </g>
    </g>
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#FFB366">荣耀时刻</text>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M0,950 Q960,900 1920,950" stroke="#4472C4" stroke-width="2" fill="none" opacity="0.2"/>
</svg>
