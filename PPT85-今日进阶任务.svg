<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,150 Q960,100 1920,150" stroke="#4472C4" stroke-width="2" fill="none" opacity="0.2"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="#2F4F4F">今日进阶挑战："双料英雄"任务</text>
  
  <!-- 挑战一 -->
  <g transform="translate(120,280)">
    <rect x="0" y="0" width="800" height="400" rx="25" fill="#E8F4FD" stroke="#4472C4" stroke-width="4"/>
    <!-- 完整流程图标 -->
    <g transform="translate(400,80)">
      <circle cx="0" cy="0" r="40" fill="#4472C4"/>
      <!-- SPIN流程图标 -->
      <text x="-15" y="-10" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#ffffff">S</text>
      <text x="5" y="-10" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#ffffff">P</text>
      <text x="-15" y="10" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#ffffff">I</text>
      <text x="5" y="10" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#ffffff">N</text>
      <path d="M-20,-20 L20,20" stroke="#ffffff" stroke-width="2"/>
      <path d="M20,-20 L-20,20" stroke="#ffffff" stroke-width="2"/>
    </g>
    <text x="400" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#4472C4">挑战一：打通SPIN全流程</text>
    
    <text x="50" y="230" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2F4F4F">任务:</text>
    <text x="50" y="270" font-family="Microsoft YaHei" font-size="26" fill="#2F4F4F">完成一次从S → P → I → N的完整提问，</text>
    <text x="50" y="310" font-family="Microsoft YaHei" font-size="26" fill="#2F4F4F">并成功呈现解决方案。</text>
    
    <rect x="50" y="340" width="200" height="40" rx="20" fill="#4472C4"/>
    <text x="150" y="365" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ffffff">#逻辑大师#</text>
  </g>
  
  <!-- 挑战二 -->
  <g transform="translate(1000,280)">
    <rect x="0" y="0" width="800" height="400" rx="25" fill="#FFF8E1" stroke="#FFB366" stroke-width="4"/>
    <!-- 盾牌反击图标 -->
    <g transform="translate(400,80)">
      <path d="M0,-35 Q-25,-20 -25,10 Q-25,35 0,40 Q25,35 25,10 Q25,-20 0,-35 Z" fill="#FFB366"/>
      <circle cx="0" cy="0" r="15" fill="#ffffff"/>
      <path d="M-8,-5 L0,8 L8,-5" stroke="#FFB366" stroke-width="3" fill="none"/>
    </g>
    <text x="400" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#FFB366">挑战二：成功化解一次真实异议</text>
    
    <text x="50" y="230" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2F4F4F">任务:</text>
    <text x="50" y="270" font-family="Microsoft YaHei" font-size="26" fill="#2F4F4F">运用LSCPA模型，有效应对一次客户的</text>
    <text x="50" y="310" font-family="Microsoft YaHei" font-size="26" fill="#2F4F4F">价格/需求/信任异议。</text>
    
    <rect x="50" y="340" width="200" height="40" rx="20" fill="#FFB366"/>
    <text x="150" y="365" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ffffff">#拆弹专家#</text>
  </g>
  
  <!-- 底部醒目大字 -->
  <g transform="translate(960,750)">
    <rect x="-450" y="-40" width="900" height="100" rx="20" fill="#FF6B6B"/>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="44" font-weight="bold" fill="#ffffff">任选其一，挑战成功，你就是本次培训的终极英雄！</text>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M0,950 Q960,900 1920,950" stroke="#4472C4" stroke-width="2" fill="none" opacity="0.2"/>
</svg>
