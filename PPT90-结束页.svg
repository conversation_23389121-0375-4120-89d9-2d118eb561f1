<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 旭日东升的地平线背景 -->
  <defs>
    <linearGradient id="sunrise" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" style="stop-color:#4472C4;stop-opacity:0.1"/>
      <stop offset="50%" style="stop-color:#FFB366;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#FFB366;stop-opacity:0.1"/>
    </linearGradient>
  </defs>
  <rect width="1920" height="1080" fill="url(#sunrise)"/>
  
  <!-- 地平线和道路 -->
  <g transform="translate(0,600)">
    <!-- 地平线 -->
    <path d="M0,200 Q960,150 1920,200" stroke="#4472C4" stroke-width="3" fill="none"/>
    
    <!-- 道路 -->
    <path d="M960,480 Q960,400 960,200" stroke="#4472C4" stroke-width="8" fill="none"/>
    <path d="M900,480 Q920,350 940,200" stroke="#4472C4" stroke-width="6" fill="none"/>
    <path d="M1020,480 Q1000,350 980,200" stroke="#4472C4" stroke-width="6" fill="none"/>
    
    <!-- 道路中线 -->
    <path d="M960,480 L960,450 M960,420 L960,390 M960,360 L960,330 M960,300 L960,270 M960,240 L960,210" stroke="#ffffff" stroke-width="3" stroke-dasharray="15,10"/>
  </g>
  
  <!-- 太阳 -->
  <g transform="translate(960,400)">
    <circle cx="0" cy="0" r="60" fill="#FFB366" opacity="0.8"/>
    <circle cx="0" cy="0" r="40" fill="#FFB366"/>
    <!-- 光芒 -->
    <g stroke="#FFB366" stroke-width="4" opacity="0.6">
      <line x1="0" y1="-100" x2="0" y2="-80"/>
      <line x1="0" y1="80" x2="0" y2="100"/>
      <line x1="-100" y1="0" x2="-80" y2="0"/>
      <line x1="80" y1="0" x2="100" y2="0"/>
      <line x1="-71" y1="-71" x2="-57" y2="-57"/>
      <line x1="57" y1="57" x2="71" y2="71"/>
      <line x1="71" y1="-71" x2="57" y2="-57"/>
      <line x1="-57" y1="57" x2="-71" y2="71"/>
    </g>
  </g>
  
  <!-- 主标题 - 书法字体效果 -->
  <g transform="translate(960,200)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#2F4F4F" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3)">培训的结束，才是真正的开始</text>
  </g>
  
  <!-- 底部祝福语 -->
  <g transform="translate(960,850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#4472C4">愿我们今天播下的种子，在未来的每一天里，生根、发芽、开花、结果。</text>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#4472C4">祝各位，前程似锦，顶峰相会！</text>
  </g>
  
  <!-- 装饰云朵 -->
  <g transform="translate(300,300)" opacity="0.3">
    <ellipse cx="0" cy="0" rx="40" ry="20" fill="#ffffff"/>
    <ellipse cx="30" cy="-5" rx="30" ry="15" fill="#ffffff"/>
    <ellipse cx="-25" cy="-8" rx="25" ry="12" fill="#ffffff"/>
  </g>
  
  <g transform="translate(1500,350)" opacity="0.3">
    <ellipse cx="0" cy="0" rx="35" ry="18" fill="#ffffff"/>
    <ellipse cx="25" cy="-3" rx="25" ry="12" fill="#ffffff"/>
    <ellipse cx="-20" cy="-5" rx="20" ry="10" fill="#ffffff"/>
  </g>
</svg>
