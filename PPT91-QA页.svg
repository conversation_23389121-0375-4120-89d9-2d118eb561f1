<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,150 Q960,100 1920,150" stroke="#4472C4" stroke-width="2" fill="none" opacity="0.2"/>
  
  <!-- 大标题 Q&A -->
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="120" font-weight="bold" fill="#4472C4">Q&A</text>
  
  <!-- 副标题 -->
  <text x="960" y="350" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#2F4F4F">互动答疑 & 资源分享</text>
  
  <!-- 讲师信息框 -->
  <g transform="translate(960,500)">
    <rect x="-300" y="-100" width="600" height="200" rx="25" fill="#F8F9FA" stroke="#4472C4" stroke-width="3"/>
    
    <!-- 讲师头像占位 -->
    <circle cx="0" cy="-30" r="40" fill="#E8F4FD" stroke="#4472C4" stroke-width="2"/>
    <circle cx="0" cy="-40" r="15" fill="#FFB366"/>
    <rect x="-20" y="-15" width="40" height="30" rx="20" fill="#4472C4"/>
    
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#4472C4">讲师姓名: XXX</text>
  </g>
  
  <!-- 联系方式 -->
  <g transform="translate(960,750)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2F4F4F">联系方式:</text>
    
    <!-- 微信二维码占位 -->
    <g transform="translate(0,80)">
      <rect x="-60" y="-60" width="120" height="120" rx="10" fill="#F8F9FA" stroke="#4472C4" stroke-width="2"/>
      <!-- 二维码图案 -->
      <g fill="#4472C4">
        <rect x="-50" y="-50" width="20" height="20"/>
        <rect x="-20" y="-50" width="10" height="10"/>
        <rect x="30" y="-50" width="20" height="20"/>
        <rect x="-50" y="-20" width="10" height="10"/>
        <rect x="0" y="-20" width="10" height="10"/>
        <rect x="30" y="-20" width="10" height="10"/>
        <rect x="-50" y="30" width="20" height="20"/>
        <rect x="-10" y="30" width="10" height="10"/>
        <rect x="20" y="30" width="10" height="10"/>
        <rect x="40" y="40" width="10" height="10"/>
      </g>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#4472C4">微信二维码</text>
    </g>
  </g>
  
  <!-- 问答图标装饰 -->
  <g transform="translate(300,400)">
    <circle cx="0" cy="0" r="50" fill="#E8F4FD" stroke="#4472C4" stroke-width="3"/>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#4472C4">?</text>
  </g>
  
  <g transform="translate(1620,400)">
    <circle cx="0" cy="0" r="50" fill="#FFF8E1" stroke="#FFB366" stroke-width="3"/>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#FFB366">!</text>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M0,950 Q960,900 1920,950" stroke="#4472C4" stroke-width="2" fill="none" opacity="0.2"/>
</svg>
